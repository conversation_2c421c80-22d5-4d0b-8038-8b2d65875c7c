import contextlib
import os
import json
import time
import signal

from openai import OpenAI
from dotenv import load_dotenv

from toolcall_prompt import nshot_chats
from baseline_experiment import extract_answer, run_test_concurrent
from evaluation import delete_extra_zero

load_dotenv()

TEST_FILE = "data/GSM8K/test.jsonl"
# MODEL = "GLM-4-FlashX-250414"
MODEL = "GLM-4-Flash-250414"
MAX_CONCURRENT_WORKERS = 30     # Adjust based on rate limits and system capabilities
DEBUG = False
TEMPERATURE = 0

client = OpenAI(
	api_key=os.getenv("API_KEY"),
	base_url="https://open.bigmodel.cn/api/paas/v4/"
)

tools = [
    {
        "type": "function",
        "function": {
            "name": "python_code_executor",
            "description": "Execute python code and return the result. No I/O operations are allowed.",
            "parameters": {
                "type": "object",
                "properties": {
                    "code": {
                        "type": "string",
                        "description": "Python code to execute, make sure this is executable (no extra annotation in front or behind), you must assign result to a variable named `result` in order to get the result."
                    }
                },
                "required": ["code"]
            }
        }
    }
]

class TimeoutException(Exception):
    pass

import threading
@contextlib.contextmanager
def time_limit(seconds: float):
    def signal_handler(signum, frame):
        raise TimeoutException("Timed out!")
    
    # Set the signal handler and a alarm
    old_handler = signal.signal(signal.SIGALRM, signal_handler)
    signal.alarm(int(seconds))

    try:
        yield
    finally:
        signal.alarm(0)  # Disable the alarm
        signal.signal(signal.SIGALRM, old_handler)


def safe_inference(messages, max_retries=3, timeout=20, wait_time=3):
	for attempt in range(max_retries):
		try:
			start_time = time.time()
			response = client.chat.completions.create(
				model=MODEL,
				messages=messages,
				timeout=timeout,
				temperature=TEMPERATURE,
				tools=tools,
				tool_choice="auto",
				extra_body={
					"do_sample": False
				} if TEMPERATURE == 0 else {}
			)
			
			wall_clock_time = time.time() - start_time
			return response, wall_clock_time
		except Exception as e:
			if DEBUG: print(f"Request failed. Retrying ({attempt + 1}/{max_retries}). Error: {e}")
			if attempt < max_retries - 1:
				time.sleep(wait_time * (attempt + 1))
			else:
				return None, None
			
			
def process_single_test(test_item, n):
	question = test_item["question"]

	correct_answer = extract_answer(test_item["answer"])
	messages = nshot_chats(n, question)

	response, total_wall_clock_time = safe_inference(messages)

	if not response:
		return None
	
	num_of_requests = 1
	input_tokens = response.usage.prompt_tokens
	completion_tokens = response.usage.completion_tokens
	
	messages.append(response.choices[0].message)
	model_response = response.choices[0].message.content or ""
	model_answer = None
	
	# Handle function calls
	while response.choices[0].message.tool_calls:

		if num_of_requests > 5:
			if "result" in local_scope:
				model_answer = delete_extra_zero(local_scope["result"])
			else:
				model_answer = "N/A"
			break
		else:
			for tool_call in response.choices[0].message.tool_calls:
				if tool_call.function.name == "python_code_executor":
					args = json.loads(tool_call.function.arguments)

					try:
						# Create a sandboxed local scope to capture results
						local_scope = {}
						# Create restricted globals that disable print
						restricted_globals = {"__builtins__": {k: v for k, v in __builtins__.__dict__.items() if k not in ["print", "input", "open"]}}
						globals = {}

						with time_limit(15):
							exec(args["code"], globals, local_scope)
							result = str(local_scope)
						
						# if DEBUG: print("Execution result:\n", result)
					except TimeoutException:
						result = "Timeout, check for infinite loops."
					except Exception as e:
						result = str(e)
						if DEBUG: print("Execution error:", e)

					model_response += f"""
Code:
```python
{args["code"]}
```
Result:
{result}
"""

					if "result" in local_scope:
						model_answer = delete_extra_zero(str(local_scope["result"]))
						break 
					else:
						messages.append({
							"role": "tool",
							"content": str(result) + "\nPlease assign the result to a variable named `result`."
						})
			if model_answer:
				break
		
		response, wall_clock_time = safe_inference(messages)

		if not response:
			return None

		num_of_requests += 1
		input_tokens += response.usage.prompt_tokens
		completion_tokens += response.usage.completion_tokens
		total_wall_clock_time += wall_clock_time

		messages.append(response.choices[0].message)
		model_response += response.choices[0].message.content or ""


	accuracy = 1 if model_answer == correct_answer else 0

	return {
		"question": question,
		"correct_answer": correct_answer,
		"model_answer": model_answer,
		"accuracy": accuracy,
		"model_response": model_response,
		"wall_clock_time": total_wall_clock_time,
		"num_of_requests": num_of_requests,
		"input_tokens": input_tokens,
		"completion_tokens": completion_tokens,
	}


if __name__ == "__main__":
	run_test_concurrent(0, "zeroshot.run_python", process_single_test)
	run_test_concurrent(4, "fourshot.run_python", process_single_test)
