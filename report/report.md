## Baseline Experiments on GSM8K

I tested the performance of GLM-4-FlashX-250414 on GSM8K with greedy decoding (temperature=0) with 0-shot, 2-shot, 4-shot, and 8-shot.

### Controlled Variables

-   Model: GLM-4-FlashX-250414
-   Temperature: 0 (greedy decoding)

### Varying Variables

-   Number of demonstrations: 0, 2, 4, 8

### Results

| Number of Demonstrations | Accuracy| Average Input Tokens | Average Output Tokens | Average Wall Clock Time (s) |
|--------------------------|----------| -------------------- |-----------------------|----------------------------|
| 0 | 0.87 | 115.71 | 193.79 | 4.97 |
| 2 | 0.88 | 302.71 | 149.73 | 4.00 |
| 4 | 0.88 | 514.71 | 148.82 | 3.87 |
| 8 | 0.88 | 963.71 | 142.66 | 3.91 |

### Discussion

-   Performance does not improve with more demonstrations. 2-8 demonstrations all have the similar accuracy. 0-shot is slightly worse than 2-shot.
-   Average Output Tokens decreases with more demonstrations, this might be because the model trying to match the output to previous demonstrations, which is short and concise.
-   Average Wall Clock Inference Time is somewhat uncorrelated to the number of demonstrations. It might be due internet connection, server load, or other factors. Since we are measuring the end-to-end time, not the actual inference time, it is not a good metric to compare different settings.



## Ensemble Experiments on GSM8K
Instead of getting answer from a single model, we can get answer from multiple models and let them vote. Intuitively, this should improve the performance. I tested the performance of 3-model ensemble on GSM8K with 0-shot and 4-shot. In my implementation if there is a tie, I ask the models to vote again, until we break the tie.

### Controlled Variables

-   Model: GLM-4-FlashX-250414
-   Temperature: 0.8 (TO ensure diversity in answers)
-   Number of models: 3

### Varying Variables

-   Number of demonstrations: 0, 4

### Results

| Number of Demonstrations | Accuracy| Average Input Tokens | Average Output Tokens | Average Wall Clock Time (s) | Number of Requests |
