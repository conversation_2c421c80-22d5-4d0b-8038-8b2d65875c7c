import json

gsm8k_nshots = [
    (
        'There are 15 trees in the grove. Grove workers will plant trees in the grove today. After they are done, there will be 21 trees. How many trees did the grove workers plant today?',
        '#There are 15 trees originally\noriginal_trees = 15\n#Then there were 21 trees after the Grove workers planted some more\nplanted_trees = 21\nplanted_today = planted_trees - original_trees\nresult = planted_today',
    ),
    (
        'If there are 3 cars in the parking lot and 2 more cars arrive, how many cars are in the parking lot?',
        '#There are originally 3 cars\noriginal_cars = 3\n#Then 2 more cars arrive\narrived_cars = 2\n #Calculate the current number of cars\ncurrent_cars = original_cars + arrived_cars\nresult = current_cars',
    ),
    (
        '<PERSON> had 32 chocolates and her sister had 42. If they ate 35, how many pieces do they have left in total?',
        '#<PERSON> had 32 chocolates\nleah_chocolates = 32\n#Her sister had 42 chocolates\nsister_chocolates = 42\n#They ate 35 chocolates\neaten_chocolates = 35\n#Calculate the remaining chocolates\nremaining_chocolates = leah_chocolates + sister_chocolates - eaten_chocolates\nresult = remaining_chocolates',
    ),
    (
        '<PERSON> had 20 lollipops. He gave <PERSON> some lollipops. Now <PERSON> has 12 lollipops. How many lollipops did <PERSON> give to <PERSON>?',
        '#<PERSON> had 20 lollipops\njason_lollipops = 20\n#He gave Denny some lollipops\ndenny_lollipops = 12\n#Calculate the remaining lollipops\nremaining_lollipops = jason_lollipops - denny_lollipops\nresult = remaining_lollipops',
    ),
    (
        'Shawn has five toys. For Christmas, he got two toys each from his mom and dad. How many toys does he have now?',
        '#Shawn has five toys\nshawn_toys = 5\n#He got two toys each from his mom and dad\nreceived_toys = 2\n#Calculate the current number of toys\ncurrent_toys = shawn_toys + received_toys * 2\nresult = current_toys',
    ),
    (
        'There were nine computers in the server room. Five more computers were installed each day, from monday to thursday. How many computers are now in the server room?',
        '#There were originally 9 computers\noriginal_computers = 9\n#Five more computers were installed each day, from monday to thursday\ninstalled_computers = 5\n#Calculate the current number of computers\ncurrent_computers = original_computers + installed_computers * 4\nresult = current_computers',
    ),
    (
        'Michael had 58 golf balls. On tuesday, he lost 23 golf balls. On wednesday, he lost 2 more. How many golf balls did he have at the end of wednesday?',
        '#Michael had 58 golf balls\nmichael_golf_balls = 58\n#He lost 23 golf balls on Tuesday\nlost_tuesday = 23\n#He lost 2 more on Wednesday\nlost_wednesday = 2\n#Calculate the remaining golf balls\nremaining_golf_balls = michael_golf_balls - lost_tuesday - lost_wednesday\nresult = remaining_golf_balls',
    ),
    (
        'Olivia has $23. She bought five bagels for $3 each. How much money does she have left?',
        '#Olivia has $23\nolivia_money = 23\n#She bought five bagels for $3 each\nbagels_cost = 5 * 3\n#Calculate the remaining money\nremaining_money = olivia_money - bagels_cost\nresult = remaining_money',
    )
]


def nshot_chats(n: int, question: str) -> dict:
    def question_prompt(s):
        return f'Question: {s}'

    def answer_prompt(s):
        return f"Answer:\nLet's think step by step.\n{s}"

    chats = [
        {"role": "system", "content": "Your task is to solve a series of math word problems by providing a python code to calculate the final answer and execute the code. Alway decompose the problem into a python code by assigning value to variables and perform calculation on the variables, be consistent with indentation use 1 tab (\\t) for each level, finally assign the result to a variable named `result`. Provide the code as an argument to the python_code_executor tool, and call the python_code_executor tool to execute the code."}
    ]

    for q, a in gsm8k_nshots[:n]:
        chats.append(
            {"role": "user", "content": question_prompt(q)})
        chats.append(
            {"role": "assistant", "tool_calls": [
                {
                    "type": "function",
                    "function": {
                        "name": "python_code_executor",
                        "arguments": json.dumps({"code": a})
                    }
                }
            ]})

    chats.append({"role": "user", "content": question_prompt(question)})
    return chats


zero_shot_prompt = nshot_chats(n=0, question="Elsa has 5 apples. Anna has 2 more apples than Elsa. How many apples do they have together?")

few_shot_prompt = nshot_chats(n=2, question="Elsa has 5 apples. Anna has 2 more apples than Elsa. How many apples do they have together?")  # todo: n is the number of demonstrations