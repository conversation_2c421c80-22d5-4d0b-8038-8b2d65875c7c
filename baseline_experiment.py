from openai import OpenAI
from dotenv import load_dotenv
from tqdm import tqdm
from data.GSM8K.baseline import nshot_chats
from data.GSM8K.evaluation import extract_ans_from_response, delete_extra_zero
import os
import re
import json
import time
import requests
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
import threading
# Uncomment the line below if you want to use tabulate for better table formatting
# from tabulate import tabulate

load_dotenv()

TEST_FILE = "test.jsonl"
# MODEL = "GLM-4-FlashX-250414"
MODEL = "GLM-4-Flash-250414"
MAX_CONCURRENT_WORKERS = 65     # Adjust based on rate limits and system capabilities
DEBUG = False# Set to False to suppress all print output
TEMPERATURE = 0

client = OpenAI(
	api_key=os.getenv("API_KEY"),
	base_url="https://open.bigmodel.cn/api/paas/v4/"
)

def get_output_file_path(experiment_name: str):
	if not os.path.exists(MODEL):
		os.makedirs(MODEL)
	return os.path.join(MODEL, f"{experiment_name}.jsonl")

def extract_answer(test_solution: str):
	if not test_solution:
		return "N/A"
	answer = extract_ans_from_response(test_solution)
	if type(answer) == int:
		return str(answer)
	matches = re.findall(r'-?\d+(?:\.\d+)?(?:/\d+)?', answer)

	if not matches:
		if DEBUG: print(f"Warning: No valid number found in the answer: {answer}")
		return "N/A"
	
	return delete_extra_zero(matches[0])

def safe_inference(messages, temperature: float = TEMPERATURE, max_retries=3, timeout=20, wait_time=3):
	for attempt in range(max_retries):
		try:
			start_time = time.time()
			response = client.chat.completions.create(
				model=MODEL,
				messages=messages,
				timeout=timeout,
				temperature=temperature,
				extra_body={
					"do_sample": False
				} if temperature == 0 else {}
			)
			
			wall_clock_time = time.time() - start_time
			return response, wall_clock_time
		except Exception as e:
			if DEBUG: print(f"Request failed. Retrying ({attempt + 1}/{max_retries}). Error: {e}")
			if attempt < max_retries - 1:
				time.sleep(wait_time * (attempt + 1))
			else:
				return None, None

def process_single_test(test_item, n):
	question = test_item["question"]

	correct_answer = extract_answer(test_item["answer"])
	response, wall_clock_time = safe_inference(nshot_chats(n, question))

	if response is None:
		return None

	model_answer = extract_answer(response.choices[0].message.content)
	completion_tokens = response.usage.completion_tokens
	input_tokens = response.usage.prompt_tokens
	accuracy = 1 if model_answer == correct_answer else 0

	return {
		"question": question,
		"correct_answer": correct_answer,
		"model_answer": model_answer,
		"accuracy": accuracy,
		"model_response": response.choices[0].message.content,
		"wall_clock_time": wall_clock_time,
		"input_tokens": input_tokens,
		"completion_tokens": completion_tokens,
		"num_of_requests": 1
	}

def run_test_concurrent(n: int, experiment_name: str, test_func: callable, max_workers: int = MAX_CONCURRENT_WORKERS, run_first_n: int = 0, **kwargs):
	with open(TEST_FILE, "r") as f:
		lines = f.readlines()

	test_data = [json.loads(line) for line in lines]
	if run_first_n > 0:
		test_data = test_data[:run_first_n]
		
	success_count = 0
	correct_count = 0
	total_input_tokens = 0
	total_output_tokens = 0
	total_inference_time = 0
	total_num_of_requests = 0

	print(f"\n=== Experiment: {experiment_name} (Concurrent with {max_workers} workers) ===")

	# load existing results to resume
	processed_questions = set()
	if os.path.exists(get_output_file_path(experiment_name)):
		with open(get_output_file_path(experiment_name), "r") as f:
			existing_lines = f.readlines()
			success_count = len(existing_lines)
			for line in existing_lines:
				log = json.loads(line)
				processed_questions.add(log["question"])
				correct_count += log["accuracy"]
				total_output_tokens += log["completion_tokens"]
				total_inference_time += log["wall_clock_time"]
				total_input_tokens += log["input_tokens"] if "input_tokens" in log else 0
				total_num_of_requests += log["num_of_requests"] if "num_of_requests" in log else 1
		if success_count > 0:
			print(f"Resuming from {success_count} completed tests.")

	# Filter out already processed questions
	remaining_tests = [test for test in test_data if test["question"] not in processed_questions]

	# Thread-safe file writing
	file_lock = threading.Lock()

	# Process remaining tests concurrently
	if len(remaining_tests) > 0:
		with open(get_output_file_path(experiment_name), "a") as f:  # Append mode for resuming
			with ThreadPoolExecutor(max_workers=min(len(remaining_tests), max_workers)) as executor:
				# Submit all tasks
				futures = []
				for test in remaining_tests:
					future = executor.submit(test_func, test, n, **kwargs)
					futures.append(future)

				# Process completed tasks
				for future in tqdm(as_completed(futures), total=len(futures), desc="Processing"):
					result = future.result()
					if result is not None:
						# Thread-safe file writing and counter updates
						with file_lock:
							f.write(json.dumps(result) + "\n")
							f.flush()
							success_count += 1
							correct_count += result["accuracy"]
							total_input_tokens += result["input_tokens"]
							total_output_tokens += result["completion_tokens"]
							total_inference_time += result["wall_clock_time"]
							total_num_of_requests += result["num_of_requests"] if "num_of_requests" in result else 1

	if success_count > 0:
		accuracy = correct_count / success_count
		average_input_tokens = total_input_tokens / success_count
		average_output_tokens = total_output_tokens / success_count
		total_tokens = total_input_tokens + total_output_tokens
		average_total_tokens = total_tokens / success_count
		average_inference_time = total_inference_time / success_count
		average_num_of_requests = total_num_of_requests / success_count

		# Print results in a table format
		print_results_table(len(test_data), success_count, accuracy,
					       total_input_tokens, average_input_tokens,
		                   total_output_tokens, average_output_tokens,
		                   total_tokens, average_total_tokens,
		                   total_inference_time, average_inference_time,
		                   total_num_of_requests, average_num_of_requests)
	else:
		print("No successful inferences completed.")

def print_results_table(total_questions, success_count, accuracy,
					   total_input_tokens, average_input_tokens,
                       total_output_tokens, average_output_tokens,
                       total_tokens, average_total_tokens,
                       total_inference_time, average_inference_time,
                       total_num_of_requests, average_num_of_requests):
	"""Print experiment results in a nicely formatted table."""

	# Option 1: Simple aligned formatting
	print("-"*70)
	print(f"{'Metric':<30} {'Value':<20} {'Unit':<20}")
	print("-"*70)
	print(f"{'Total Questions':<30} {total_questions:<20} {'questions':<20}")
	print(f"{'Successful Inferences':<30} {success_count:<20} {'questions':<20}")
	print(f"{'Accuracy':<30} {accuracy:<20.4f} {'ratio':<20}")
	print(f"{'Total Input Tokens':<30} {total_input_tokens:<20} {'tokens':<20}")
	print(f"{'Average Input Tokens':<30} {average_input_tokens:<20.4f} {'tokens/question':<20}")
	print(f"{'Total Output Tokens':<30} {total_output_tokens:<20} {'tokens':<20}")
	print(f"{'Average Output Tokens':<30} {average_output_tokens:<20.4f} {'tokens/question':<20}")
	print(f"{'Total Tokens':<30} {total_tokens:<20} {'tokens':<20}")
	print(f"{'Average Total Tokens':<30} {average_total_tokens:<20.4f} {'tokens/question':<20}")
	print(f"{'Total Inference Time':<30} {total_inference_time:<20.4f} {'seconds':<20}")
	print(f"{'Average Inference Time':<30} {average_inference_time:<20.4f} {'seconds/question':<20}")
	print(f"{'Total Number of Requests':<30} {total_num_of_requests:<20} {'requests':<20}")
	print(f"{'Average Number of Requests':<30} {average_num_of_requests:<20.4f} {'requests/question':<20}")
	print("="*70)


def count_fewshot_answer_tokens():
	def count_tokens(text):
		res = requests.post(
			"https://open.bigmodel.cn/api/paas/v4/tokenizer",
			headers={
				"Authorization": os.getenv('API_KEY'),
				"Content-Type": "application/json"
			},
			json = {
				"model": "glm-4-flash",
				"messages":[
					{
						"role": "user",
						"content": text
					}
				]
			}
		)

		return res.json()["usage"]["prompt_tokens"]

	# Count the number of tokens in the few-shot examples
	fewshot_examples = nshot_chats(8, "")
	total_tokens = 0
	for message in fewshot_examples:
		if message["role"] == "assistant":
			total_tokens += count_tokens(message["content"])
	print(f"Average number of tokens in few-shot examples: {total_tokens / 8}")

if __name__ == "__main__":
	# count_fewshot_answer_tokens()

	run_test_concurrent(0, "zeroshot.baseline", process_single_test)
	run_test_concurrent(2, "twoshot.baseline", process_single_test)
	run_test_concurrent(4, "fourshot.baseline", process_single_test)
	run_test_concurrent(8, "fewshot.baseline", process_single_test)
