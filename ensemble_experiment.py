from baseline import nshot_chats
from baseline_experiment import extract_answer, run_test_concurrent, safe_inference


TEST_FILE = "test.jsonl"
# MODEL = "GLM-4-FlashX-250414"
MODEL = "GLM-4-Flash-250414"
MAX_CONCURRENT_WORKERS = 60     # Adjust based on rate limits and system capabilities
DEBUG = False
TEMPERATURE = 1
N_MODELS = 3

			
def process_single_test(test_item, n, n_models = N_MODELS):
	question = test_item["question"]

	correct_answer = extract_answer(test_item["answer"])
	messages = nshot_chats(n, question)

	num_of_requests = 0
	input_tokens = 0
	completion_tokens = 0
	total_wall_clock_time = 0

	votes = {}
	responses = []

	# Collect votes
	for _ in range(N_MODELS):
		response, wall_clock_time = safe_inference(messages, temperature=TEMPERATURE)
		if not response:
			return None

		num_of_requests += 1
		input_tokens += response.usage.prompt_tokens
		completion_tokens += response.usage.completion_tokens
		total_wall_clock_time += wall_clock_time

		model_answer = extract_answer(response.choices[0].message.content)
		votes[model_answer] = votes.get(model_answer, 0) + 1
		responses.append(response.choices[0].message.content)

	ranked_answers = sorted(votes.items(), key=lambda x: x[1], reverse=True)

	# If there is a tie, we ask the models to vote again
	if len(ranked_answers) > 1 and ranked_answers[0][1] == ranked_answers[1][1]:
		candidates = [answer for answer, vote in ranked_answers if vote == ranked_answers[0][1]]
		model_answer = None
		while model_answer not in candidates:
			response, wall_clock_time = safe_inference(messages, temperature=TEMPERATURE)
			if not response:
				return None
			
			num_of_requests += 1
			input_tokens += response.usage.prompt_tokens
			completion_tokens += response.usage.completion_tokens
			total_wall_clock_time += wall_clock_time

			model_answer = extract_answer(response.choices[0].message.content)
			responses.append(response.choices[0].message.content)
	else:
		model_answer = ranked_answers[0][0]

	accuracy = 1 if model_answer == correct_answer else 0

	return {
		"question": question,
		"correct_answer": correct_answer,
		"model_answer": model_answer,
		"accuracy": accuracy,
		"model_response": responses,
		"wall_clock_time": total_wall_clock_time,
		"num_of_requests": num_of_requests,
		"input_tokens": input_tokens,
		"completion_tokens": completion_tokens,
	}


if __name__ == "__main__":
	run_test_concurrent(0, "zeroshot.3ensembles.temp1", process_single_test, max_workers=MAX_CONCURRENT_WORKERS)
	# run_test_concurrent(0, "zeroshot.5ensembles", process_single_test, max_workers=MAX_CONCURRENT_WORKERS, n_models=5)
	run_test_concurrent(4, "fourshot.3ensembles.temp1", process_single_test, max_workers=MAX_CONCURRENT_WORKERS)
	# run_test_concurrent(4, "fourshot.5ensembles", process_single_test, max_workers=MAX_CONCURRENT_WORKERS, n_models=5)
