import json
import os

from sentence_transformers import SentenceTransformer
import pandas as pd

from baseline_experiment import extract_answer, run_test_concurrent, safe_inference

TRAIN_FILE = "train.jsonl"
TRAIN_EMBEDDING_FILE = "train.embedding.jsonl"
TEST_FILE = "test.jsonl"
TEST_EMBEDDING_FILE = "test.embedding.jsonl"
# MODEL = "GLM-4-FlashX-250414"
MODEL = "GLM-4-Flash-250414"
MAX_CONCURRENT_WORKERS = 60     # Adjust based on rate limits and system capabilities
DEBUG = True
TEMPERATURE = 0

def create_embedding(existing_embedding):
	questions_to_embed = []
	answers_to_questions = []

	with open(TRAIN_FILE, "r") as fr:
		for line in fr:
			item = json.loads(line)
			if item["question"] not in existing_embedding["question"].tolist():
				questions_to_embed.append(item["question"])
				answers_to_questions.append(item["answer"])

	question_embeddings = zip(questions_to_embed, answers_to_questions, embedding_model.encode(questions_to_embed, batch_size=128, show_progress_bar=True))

	if DEBUG: print(f"Embedding {len(questions_to_embed)} questions...")

	with open(TRAIN_EMBEDDING_FILE, "a") as fw:
		for question, answer, embedding in question_embeddings:
			fw.write(json.dumps({"question": question, "answer": answer, "embedding": embedding.tolist()}) + "\n")
		fw.flush()

	existing_embedding = pd.concat([existing_embedding, pd.DataFrame(question_embeddings)], ignore_index=True)
	
	return existing_embedding
	
def load_knowledge_base():
	if os.path.exists(TRAIN_EMBEDDING_FILE):
		existing_embedding = pd.read_json(TRAIN_EMBEDDING_FILE, lines=True)
	else:
		existing_embedding = pd.DataFrame(columns=["question", "answer", "embedding"])

	with open(TRAIN_FILE, "r") as fr:
		lines = fr.readlines()
	total_questions = len(lines)
	
	if DEBUG: print(f"Total questions: {total_questions}, existing embeddings: {len(existing_embedding)}")

	if len(existing_embedding) < total_questions:
		existing_embedding = create_embedding(existing_embedding)
	
	return existing_embedding

def batch_embed_test_questions():
	if os.path.exists(TEST_EMBEDDING_FILE):
		existing_test_questions_embeddings = pd.read_json(TEST_EMBEDDING_FILE, lines=True)
	else:
		existing_test_questions_embeddings = pd.DataFrame(columns=["question", "embedding"])

	test_questions_to_embed = []
	with open(TEST_FILE, "r") as fr:
		for line in fr:
			item = json.loads(line)
			if item["question"] not in existing_test_questions_embeddings["question"]:
				test_questions_to_embed.append(item["question"])

	test_question_embeddings = embedding_model.encode(test_questions_to_embed, batch_size=128, show_progress_bar=True)
	existing_test_questions_embeddings = pd.concat([existing_test_questions_embeddings, pd.DataFrame({"question": test_questions_to_embed, "embedding": test_question_embeddings.tolist()})], ignore_index=True)
	existing_test_questions_embeddings.to_json(TEST_EMBEDDING_FILE, orient="records", lines=True)

	return existing_test_questions_embeddings
	

def find_similar_questions(question_embedding, knowledge_base, top_k=5):
	# Calculate cosine similarity
	cosine_similarities = embedding_model.similarity(question_embedding, knowledge_base["embedding"].tolist())

	# Get top k similar questions
	top_k_indices = cosine_similarities.argsort(descending=True)[0][:top_k]
	if DEBUG: print(f"Top k indices: {top_k_indices}")
	similar_questions = knowledge_base.iloc[top_k_indices]

	return similar_questions[["question", "answer"]].to_dict("records")

def relevant_nshot_chats(n: int, question: str, question_embedding, knowledge_base) -> dict:
	def question_prompt(s):
		return f'Question: {s}'

	def answer_prompt(s):
		return f"Answer:\nLet's think step by step.\n{s}"
	
	similar_questions = find_similar_questions(question_embedding, knowledge_base, top_k=n)

	chats = [
		{"role": "system", "content": "Your task is to solve a series of math word problems by providing the final answer. Use the format #### [value] to highlight your answer. For example, if the answer is 560, you should write #### 560."}
	]

	for q, a in similar_questions:
		chats.append(
			{"role": "user", "content": question_prompt(q)})
		chats.append(
			{"role": "assistant", "content": answer_prompt(a)})

	chats.append({"role": "user", "content": question_prompt(question)})
	return chats

			
def process_single_test(test_item, n, knowledge_base, test_questions_embeddings):
	question = test_item["question"]

	correct_answer = extract_answer(test_item["answer"])

	question_embedding = test_questions_embeddings[test_questions_embeddings["question"] == question]["embedding"].values[0]
	messages = relevant_nshot_chats(n, question,question_embedding, knowledge_base)

	response, wall_clock_time = safe_inference(messages, temperature=TEMPERATURE)
	if not response:
		return None

	num_of_requests = 1
	input_tokens = response.usage.prompt_tokens
	completion_tokens = response.usage.completion_tokens
	total_wall_clock_time = wall_clock_time

	model_answer = extract_answer(response.choices[0].message.content)

	accuracy = 1 if model_answer == correct_answer else 0

	return {
		"question": question,
		"correct_answer": correct_answer,
		"model_answer": model_answer,
		"accuracy": accuracy,
		"model_response": response.choices[0].message.content,
		"wall_clock_time": total_wall_clock_time,
		"num_of_requests": num_of_requests,
		"input_tokens": input_tokens,
		"completion_tokens": completion_tokens,
	}


if __name__ == "__main__":

	global embedding_model
embedding_model = SentenceTransformer("google/embeddinggemma-300m")

	kb = load_knowledge_base()
	test_questions_embeddings = batch_embed_test_questions()

	run_test_concurrent(2, "twoshot.retrieval", process_single_test, knowledge_base=kb, test_questions_embeddings=test_questions_embeddings)
	run_test_concurrent(4, "fourshot.retrieval", process_single_test, knowledge_base=kb, test_questions_embeddings=test_questions_embeddings)
	run_test_concurrent(8, "eightshot.retrieval", process_single_test, knowledge_base=kb, test_questions_embeddings=test_questions_embeddings)
